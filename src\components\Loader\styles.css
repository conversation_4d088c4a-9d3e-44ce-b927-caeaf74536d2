.loader-container {
  @apply flex items-center justify-center;
  height: calc(100vh + 101px);
}
.loader-container.sm {
  @apply h-auto;
}
.loader-container.sm .loader {
  @apply w-[20px] h-[20px];
}
.loader-container.sm.project .loader {
  @apply w-[20px] h-[20px] border-t-gray-400;
}
.loader-container .loader {
  @apply w-[48px] h-[48px] rounded-full inline-block border-t-[3px] border-t-brand-secondary border-r-[3px] border-r-transparent box-border;
  animation: rotation 1s linear infinite;
}
.loader-container .loader-alt {
  width: 200px;
  height: 140px;
  background: #979794;
  box-sizing: border-box;
  position: relative;
  border-radius: 8px;
  perspective: 1000px;
}
.loader-container .loader-alt:before {
  content: "";
  position: absolute;
  left: 10px;
  right: 10px;
  top: 10px;
  bottom: 10px;
  border-radius: 8px;
  background: #f5f5f5 no-repeat;
  background-size: 60px 10px;
  background-image: linear-gradient(#ddd 100px, transparent 0),
    linear-gradient(#ddd 100px, transparent 0),
    linear-gradient(#ddd 100px, transparent 0),
    linear-gradient(#ddd 100px, transparent 0),
    linear-gradient(#ddd 100px, transparent 0),
    linear-gradient(#ddd 100px, transparent 0);

  background-position: 15px 30px, 15px 60px, 15px 90px, 105px 30px, 105px 60px,
    105px 90px;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.25);
}
.loader-container .loader-alt:after {
  content: "";
  position: absolute;
  width: calc(50% - 10px);
  right: 10px;
  top: 10px;
  bottom: 10px;
  border-radius: 8px;
  background: #fff no-repeat;
  background-size: 60px 10px;
  background-image: linear-gradient(#ddd 100px, transparent 0),
    linear-gradient(#ddd 100px, transparent 0),
    linear-gradient(#ddd 100px, transparent 0);
  background-position: 50% 30px, 50% 60px, 50% 90px;
  transform: rotateY(0deg);
  transform-origin: left center;
  animation: paging 1s linear infinite;
}

@keyframes paging {
  to {
    transform: rotateY(-180deg);
  }
}

@keyframes rotation {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
