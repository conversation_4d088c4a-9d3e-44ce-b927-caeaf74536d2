import React from "react";
import Modal from "..";
import { Raleway } from "next/font/google";
import { Error } from "@public/assets/icons";
import { ErrorModalProps } from "./typings";

const raleway = Raleway({
  display: "swap",
  weight: ["400", "500", "600"],
  subsets: ["latin"],
});
const ErrorModal: React.FC<ErrorModalProps> = ({ errorObj, open, setOpen }) => {
  return (
    <>
      {errorObj.error && (
        <Modal
          open={open}
          setOpen={setOpen}
          styles="items-center justify-center"
        >
          <div
            className={`${raleway.className} flex flex-col space-y-4 text-grayish text-center font-medium items-center max-w-lg`}
          >
            <span>
              <Error />
            </span>
            <span className="text-[26px]">Oops!</span>
            <span className="text-sm">
              {errorObj.message || "Something went wrong"}
            </span>
          </div>
        </Modal>
      )}
    </>
  );
};

export default ErrorModal;
