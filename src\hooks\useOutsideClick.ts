import { useEffect, RefObject } from "react";

const useOutsideClick = <T extends HTMLElement>(
  ref: RefObject<T>,
  callback: () => void
) => {
  const handleClick = (e: MouseEvent) => {
    if (ref.current && !ref.current.contains(e.target as Node)) {
      callback();
    }
  };
  const handleKeyPress = (e: KeyboardEvent) => {
    if (e.key === "Escape") {
      callback();
    }
  };

  useEffect(() => {
    const handleMouseDown = (e: MouseEvent) => handleClick(e);
    const handleKeyDown = (e: KeyboardEvent) => handleKeyPress(e);

    document.addEventListener("mousedown", handleMouseDown);
    document.addEventListener("keydown", handleKeyDown);

    return () => {
      document.removeEventListener("mousedown", handleMouseDown);
      document.removeEventListener("keydown", handleKeyDown);
    };
  }, [ref, callback]);
};

export default useOutsideClick;
