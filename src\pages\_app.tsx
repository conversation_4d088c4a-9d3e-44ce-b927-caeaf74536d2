import "@styles/globals.css";
import "@fortawesome/fontawesome-free/css/all.css";
import Layout from "@components/Layout";
import type { AppProps } from "next/app";
import { Amplify } from "aws-amplify";
import { AuthProvider } from "@contexts/AuthContext";
import { config } from "@constants/cognito";

Amplify.configure(config);

export default function App({ Component, pageProps }: AppProps) {
  return (
    <AuthProvider>
      <Layout>
        <Component {...pageProps} />
      </Layout>
    </AuthProvider>
  );
}
