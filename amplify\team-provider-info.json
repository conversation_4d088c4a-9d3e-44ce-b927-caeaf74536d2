{"dev": {"awscloudformation": {"AuthRoleName": "amplify-marutidsdemofrontend-dev-3e8ae-authRole", "UnauthRoleArn": "arn:aws:iam::851725323009:role/amplify-marutidsdemofrontend-dev-3e8ae-unauthRole", "AuthRoleArn": "arn:aws:iam::851725323009:role/amplify-marutidsdemofrontend-dev-3e8ae-authRole", "Region": "us-east-1", "DeploymentBucketName": "amplify-marutidsdemofrontend-dev-3e8ae-deployment", "UnauthRoleName": "amplify-marutidsdemofrontend-dev-3e8ae-unauthRole", "StackName": "amplify-marutidsdemofrontend-dev-3e8ae", "StackId": "arn:aws:cloudformation:us-east-1:851725323009:stack/amplify-marutidsdemofrontend-dev-3e8ae/f31a5620-32df-11ef-a3c5-0affd925f63d", "AmplifyAppId": "d368v9mx2mjiiy"}, "categories": {"auth": {"marutidsdemofrontendf9d8322d": {}}}}}