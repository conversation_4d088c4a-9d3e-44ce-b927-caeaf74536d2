import { <PERSON><PERSON><PERSON><PERSON>, Loader } from "@components";
import { v4 as uuidv4 } from "uuid";
import React, { useCallback, useEffect, useRef, useState } from "react";
import { MAX_PAGES_LIMIT, SOMETHING_WENT_WRONG } from "@constants";
import { MedicalInsights, PocHeader } from "./components";
import {
  ErrorState,
  Project,
  ProjectDocument,
  Sample,
  SampleState,
} from "@typings";
import { fetchCsrfToken, getFileUploadErrors, isFileTypeAllowed } from "@utils";
import { getNumberOfPages } from "@utils/pdfUtils";
import ErrorModal from "@components/Modal/ErrorModal";
import { sections } from "@constants/tabs";
import {
  createDocuments,
  retrieveProject,
  updateProjectPages,
  updateProjectStatus,
} from "@utils/dbActions";
import { useProjects } from "@hooks/useProjects";
import { buckets } from "@constants/buckets";
import { statuses } from "@constants/db";
import { fetchJsonFromS3API } from "@utils/medicalApi";

const MedicalPocLayout: React.FC = () => {
  const [data, setData] = useState<any>({});
  const [loading, setLoading] = useState<boolean>(false);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [sample, setSample] = useState<SampleState>({
    path: "",
    index: -1,
  });
  const [activeSection, setActiveSection] = useState<string>(sections.summary);
  const [uploadLoader, setUploadLoader] = useState<boolean>(false);
  const [details, setDetails] = useState<any>();
  const [uploadError, setUploadError] = useState<ErrorState>({
    error: false,
    message: "",
  });
  const [QAError, setQAError] = useState<ErrorState>({
    error: false,
    message: "",
  });
  const [projectStatus, setProjectStatus] = useState<string>("");
  const [projectError, setProjectError] = useState<ErrorState>({
    error: false,
    message: "",
  });
  const [isModalOpen, setIsModalOpen] = useState<boolean>(false);
  const [isQAModalOpen, setIsQAModalOpen] = useState<boolean>(false);
  const [isProjectErrorModalOpen, setIsProjectErrorModalOpen] =
    useState<boolean>(false);
  let userId: string | any = "";
  const [sampleResult, setSampleResult] = useState<boolean>(false);

  const getSampleDetails = async (path: string) => {
    try {
      setUploadLoader(true);
      const sampleData = await fetchJsonFromS3API(buckets.mi, path);
      setSampleResult(true);
      setDetails(sampleData);
    } catch (error: any) {
      setSampleResult(false);
      setIsProjectErrorModalOpen(true);
      setProjectError({ error: true, message: error.message });
    } finally {
      setUploadLoader(false);
    }
  };

  const {
    projects,
    selectedProject,
    handleSelectedProject,
    setTheSelectedProject,
    handleProjectSubmission,
    projectLoading,
  } = useProjects(
    setIsProjectErrorModalOpen,
    setProjectError,
    setDetails,
    projectStatus,
    setProjectStatus,
    setUploadLoader
  );

  useEffect(() => {
    if (window && typeof window !== undefined) {
      const storedUserId = window.localStorage.getItem("userId");
      userId = storedUserId;

      const currentProject = JSON.parse(
        window.localStorage.getItem("currentProject")!
      );
      const currentSample = JSON.parse(
        window.localStorage.getItem("currentSample")!
      );
      if (
        currentProject &&
        currentProject !== null &&
        currentProject?.project_id !== -1
      ) {
        window.localStorage.removeItem("currentSample");
        setTheSelectedProject(currentProject);
        handleSelectedProject(currentProject, setUploadLoader);
      } else if (currentSample && currentSample !== null) {
        window.localStorage.removeItem("currentProject");
        setSample({ path: currentSample?.path, index: currentSample?.index });
        (async () => await getSampleDetails(currentSample?.jsonPath))();
      }
    }
  }, []);

  useEffect(() => {
    async function getPageData() {
      try {
        setLoading(true);
        const response = await fetch(`/api/get-page-data`, {
          method: "GET",
        });
        if (response) {
          const responseData = await response.json();
          setData(responseData);
        }
      } catch (error: any) {
        console.error(error);
      } finally {
        setLoading(false);
      }
    }
    getPageData();
  }, []);

  const handleSample = (path: string | any, index: number) => {
    setSample({ path, index });
  };

  const uploadMultipleFiles = useCallback(
    async (files: FileList, project: any) => {
      const { project_id } = project;
      const formData = new FormData();
      const filePaths: string[] = [];
      if (files) {
        try {
          setUploadLoader(true);
          Array.from(files).forEach((file, index) => {
            const key = `user-data/${userId}/${project_id}/request/${file.name}`;
            formData.append(`file[${index}]`, file);
            formData.append("userId", userId);
            formData.append("keys", key);
            const filePath = `s3://${buckets.mi}/${key}`;
            filePaths.push(filePath);
          });
          const csrfToken = await fetchCsrfToken();
          const response = await fetch("/api/upload", {
            method: "POST",
            headers: {
              "X-CSRF-Token": csrfToken,
            },
            body: formData,
          });

          if (!response.ok) {
            throw new Error("Failed to upload files");
          } else {
            const arrayOfPages = (await getNumberOfPages(files)).pages;
            const totalPages = (await getNumberOfPages(files)).totalPages;
            const documents: ProjectDocument[] = [];
            Array.from(files).forEach((file, index: number) => {
              documents.push({
                document_id: uuidv4(),
                document_name: file.name,
                pages: arrayOfPages?.[index],
                project_id,
              });
            });

            await createDocuments(documents, csrfToken);
            const retrievedProject = await retrieveProject(project_id);
            if (retrievedProject.ok) {
              const project = await retrievedProject.json();
              await updateProjectPages(
                project_id,
                +project?.total_pages + totalPages,
                csrfToken
              );
            }
            const data = await response.json();
            if (data && data.documentPaths && data.documentPaths.length > 0) {
              const sqsResponse = await fetch("/api/sendMessage", {
                method: "POST",
                headers: {
                  "Content-Type": "application/json",
                  "X-CSRF-Token": csrfToken,
                },
                body: JSON.stringify({
                  document_paths: data.documentPaths,
                }),
              });
              if (sqsResponse.ok) {
                await updateProjectStatus(
                  project_id,
                  statuses.Processing,
                  csrfToken
                );
                setProjectStatus(statuses.Processing);
              }
            }
          }
        } catch (error: any) {
          console.log({ error });
          setIsModalOpen(true);
          setUploadError({ error: true, message: error.message });
        } finally {
          setUploadLoader(false);
        }
      }
    },
    []
  );

  const warnUploadErrorsForMultipleFiles = (
    hasInvalidFileTypes: boolean,
    isPagesLimitExceeded: boolean
  ) => {
    if (isPagesLimitExceeded) {
      setUploadError({
        error: true,
        message: `One or more files have more than ${MAX_PAGES_LIMIT} pages.`,
      });
      setIsModalOpen(true);
    } else if (hasInvalidFileTypes) {
      setUploadError({
        error: true,
        message: `Unsupported media type, only pdf format is supported`,
      });
      setIsModalOpen(true);
    }
  };

  const handleMultipleFiles = useCallback(
    async (event: React.ChangeEvent<HTMLInputElement>, project?: Project) => {
      event.preventDefault();
      const files = event.target.files;
      if (files) {
        const filesArray = Array.from(files);
        const { hasInvalidFileTypes } = getFileUploadErrors(filesArray);
        setUploadLoader(true);
        const totalPDFPages = (await getNumberOfPages(files)).totalPages;
        setUploadLoader(false);
        const isPagesLimitExceeded = totalPDFPages > MAX_PAGES_LIMIT;
        warnUploadErrorsForMultipleFiles(
          hasInvalidFileTypes,
          isPagesLimitExceeded
        );
        let areValidFiles = false;
        areValidFiles = filesArray.every((file: File) =>
          isFileTypeAllowed(file.type)
        );
        if (areValidFiles && !isPagesLimitExceeded) {
          handleSample("", -1);
          await uploadMultipleFiles(files, project);
          if (fileInputRef && fileInputRef.current) {
            fileInputRef.current.value = "";
          }
        }
      }
    },
    []
  );

  const handleMultipleDrop = useCallback(
    async (
      event: React.DragEvent<HTMLDivElement | HTMLLabelElement>,
      project?: Project
    ) => {
      event.preventDefault();
      const files = event.dataTransfer.files;
      if (files) {
        const filesArray = Array.from(files);
        const { hasInvalidFileTypes } = getFileUploadErrors(filesArray);
        setUploadLoader(true);
        const totalPDFPages = (await getNumberOfPages(files)).totalPages;
        setUploadLoader(false);
        const isPagesLimitExceeded = totalPDFPages > MAX_PAGES_LIMIT;
        warnUploadErrorsForMultipleFiles(
          hasInvalidFileTypes,
          isPagesLimitExceeded
        );
        let areValidFiles = false;
        areValidFiles = filesArray.every((file: File) =>
          isFileTypeAllowed(file.type)
        );
        if (areValidFiles && !isPagesLimitExceeded) {
          handleSample("", -1);
          await uploadMultipleFiles(files, project);
        }
      }
    },
    []
  );

  const handleDownloadSample = async (sample: Sample) => {
    const fileName = sample?.fileName;
    const bucketName = buckets.mi;
    const bucketKey = `sample-data/sample${sample.sampleInd}_v1.1/request/${fileName}`;
    try {
      setUploadError({ error: false, message: "" });
      setUploadLoader(true);
      const response = await fetch(
        `/api/samples/download-file?fileName=${fileName}&bucketName=${bucketName}&bucketKey=${bucketKey}`
      );
      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement("a");
      link.href = url;
      link.setAttribute("download", fileName);
      document.body.appendChild(link);
      link.click();
      link.parentNode!.removeChild(link);
      window.URL.revokeObjectURL(url);
    } catch (error: any) {
      console.error("Error downloading file:", error);
      setIsModalOpen(true);
      setUploadError({
        error: true,
        message: error?.message || SOMETHING_WENT_WRONG,
      });
    } finally {
      setUploadLoader(false);
    }
  };

  const resetSample = () => {
    setSample({ path: "", index: -1 });
  };
  return (
    <>
      {loading ? (
        <Loader />
      ) : (
        <>
          <PocHeader
            src={data?.src}
            title={data?.title}
            subtitle={data?.subtitle}
          />
          <MedicalInsights
            data={data}
            sampleIndex={sample?.index as number}
            resetSample={resetSample}
            medicalDetails={details}
            handleMultipleFiles={handleMultipleFiles}
            handleMultipleDrop={handleMultipleDrop}
            handleSample={handleSample}
            handleUploadLoader={setUploadLoader}
            activeSection={activeSection}
            handleChangeSection={setActiveSection}
            handleQAModal={setIsQAModalOpen}
            handleQAError={setQAError}
            handleDownloadSample={handleDownloadSample}
            projectStatus={projectStatus}
            projects={projects}
            selectedProject={selectedProject}
            handleSelectedProject={handleSelectedProject}
            setTheSelectedProject={setTheSelectedProject}
            handleProjectSubmission={handleProjectSubmission}
            projectLoading={projectLoading}
            sampleResult={sampleResult}
            handleSampleResult={setSampleResult}
            getSampleDetails={getSampleDetails}
          />
          {uploadLoader && <BookLoader />}
          <ErrorModal
            errorObj={uploadError}
            open={isModalOpen}
            setOpen={setIsModalOpen}
          />
          <ErrorModal
            errorObj={QAError}
            open={isQAModalOpen}
            setOpen={setIsQAModalOpen}
          />
          <ErrorModal
            errorObj={projectError}
            open={isProjectErrorModalOpen}
            setOpen={setIsProjectErrorModalOpen}
          />
        </>
      )}
    </>
  );
};

export default MedicalPocLayout;
