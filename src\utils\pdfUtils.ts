import { pdfjs } from "react-pdf";

export const getNumberOfPages = async (
  files: FileList
): Promise<{ pages: number[]; totalPages: number }> => {
  pdfjs.GlobalWorkerOptions.workerSrc = `//cdnjs.cloudflare.com/ajax/libs/pdf.js/${pdfjs.version}/pdf.worker.min.js`;
  const totalPagesArray: number[] = [];
  for (const file of Array.from(files)) {
    if (file.type === "application/pdf") {
      try {
        const pdfUrl = URL.createObjectURL(file);
        const loadingTask = pdfjs.getDocument({ url: pdfUrl });
        const pdf = await loadingTask.promise;
        const numPages = pdf.numPages;
        totalPagesArray.push(numPages);
      } catch (error) {
        console.error("Error loading PDF:", error);
      }
    }
  }
  return {
    pages: totalPagesArray,
    totalPages: totalPagesArray.reduce((acc, curr) => acc + curr, 0),
  };
};

export const getPromisesOfFirstPageImageOfPDF = async (files: FileList) => {
  pdfjs.GlobalWorkerOptions.workerSrc = `//cdnjs.cloudflare.com/ajax/libs/pdf.js/${pdfjs.version}/pdf.worker.min.js`;
  const promises: Promise<string>[] = Array.from(files).map((file) => {
    return new Promise<string>((resolve, reject) => {
      const fileReader = new FileReader();
      fileReader.onload = async () => {
        const pdfBlob = new Blob([fileReader.result as ArrayBuffer], {
          type: "application/pdf",
        });
        const pdfUrl = URL.createObjectURL(pdfBlob);
        try {
          const loadingTask = pdfjs.getDocument({ url: pdfUrl });
          const pdf = await loadingTask.promise;
          const page = await pdf.getPage(1);
          const viewport = page.getViewport({ scale: 1.0 });
          const canvas = document.createElement("canvas");
          const context = canvas.getContext("2d");
          if (!context) {
            reject("Canvas context is null");
            return;
          }
          canvas.height = viewport.height;
          canvas.width = viewport.width;
          const renderContext = {
            canvasContext: context,
            viewport: viewport,
          };
          await page.render(renderContext).promise;
          const imageDataUrl = canvas.toDataURL();
          resolve(imageDataUrl);
        } catch (error) {
          reject(error);
        }
      };
      fileReader.onerror = (error) => {
        reject(error);
      };
      fileReader.readAsArrayBuffer(file);
    });
  });
  return promises;
};

export const getImageDataURL = async (data: Uint8Array) => {
  pdfjs.GlobalWorkerOptions.workerSrc = `//cdnjs.cloudflare.com/ajax/libs/pdf.js/${pdfjs.version}/pdf.worker.min.js`;
  const pdfDocument = await pdfjs.getDocument({ data }).promise;
  const page = await pdfDocument.getPage(1);
  const viewport = page.getViewport({ scale: 1 });
  const canvas = document.createElement("canvas");
  const canvasContext = canvas.getContext("2d");
  if (!canvasContext) return;
  canvas.height = viewport.height;
  canvas.width = viewport.width;
  const renderContext = {
    canvasContext,
    viewport,
  };
  await page.render(renderContext).promise;
  const imageData = canvas
    .toDataURL("image/jpeg")
    .replace(/^data:image\/jpeg;base64,/, "");
  return imageData;
};
